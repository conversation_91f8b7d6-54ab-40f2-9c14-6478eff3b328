'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { leadSchema, type LeadInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormField, FormLabel, FormMessage } from '@/components/ui/form'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Plus, Search, Filter, Edit, Trash2, Mail, Phone, Globe, MapPin } from 'lucide-react'

interface Lead {
  id: string
  name: string
  location?: string
  phone?: string
  email?: string
  website?: string
  notes?: string
  source?: string
  status: 'NEW' | 'CONTACTED' | 'INTERESTED' | 'CONVERTED' | 'LOST'
  createdAt: string
  updatedAt: string
  _count: {
    leadNotes: number
    followUps: number
  }
}

const statusColors = {
  NEW: 'bg-blue-100 text-blue-800 border-blue-200',
  CONTACTED: 'bg-gray-100 text-gray-800 border-gray-200',
  INTERESTED: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  CONVERTED: 'bg-green-100 text-green-800 border-green-200',
  LOST: 'bg-red-100 text-red-800 border-red-200',
}

const statusIcons = {
  NEW: '🆕',
  CONTACTED: '📞',
  INTERESTED: '⭐',
  CONVERTED: '✅',
  LOST: '❌',
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingLead, setEditingLead] = useState<Lead | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [sourceFilter, setSourceFilter] = useState('')

  const form = useForm<LeadInput>({
    resolver: zodResolver(leadSchema),
    defaultValues: {
      name: '',
      location: '',
      phone: '',
      email: '',
      website: '',
      notes: '',
      source: '',
      status: 'NEW',
    },
  })

  useEffect(() => {
    fetchLeads()
  }, [statusFilter, sourceFilter])

  const fetchLeads = async () => {
    try {
      const params = new URLSearchParams()
      if (statusFilter) params.append('status', statusFilter)
      if (sourceFilter) params.append('source', sourceFilter)
      
      const response = await fetch(`/api/leads?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLeads(data.leads)
      }
    } catch (error) {
      // Error fetching leads - handle silently in production
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: LeadInput) => {
    try {
      const url = editingLead ? `/api/leads/${editingLead.id}` : '/api/leads'
      const method = editingLead ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await fetchLeads()
        setIsCreateDialogOpen(false)
        setIsEditDialogOpen(false)
        setEditingLead(null)
        form.reset()
      }
    } catch (error) {
      // Error saving lead - handle silently in production
    }
  }

  const handleEdit = (lead: Lead) => {
    setEditingLead(lead)
    form.reset({
      name: lead.name,
      location: lead.location || '',
      phone: lead.phone || '',
      email: lead.email,
      website: lead.website || '',
      notes: lead.notes || '',
      source: lead.source || '',
      status: lead.status,
    })
    setIsEditDialogOpen(true)
  }

  const handleDelete = async (leadId: string) => {
    if (!confirm('Are you sure you want to delete this lead?')) return

    try {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchLeads()
      }
    } catch (error) {
      // Error deleting lead - handle silently in production
    }
  }

  const filteredLeads = leads.filter(lead =>
    lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (lead.email && lead.email.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Leads</h1>
          <p className="text-gray-600 mt-1">
            Manage and track your sales leads through the entire pipeline.
          </p>
        </div>
        <Button
          onClick={() => setIsCreateDialogOpen(true)}
          className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Lead
        </Button>
      </div>

      {/* Filters */}
      <Card className="shadow-sm border-gray-200">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Filter className="h-5 w-5 text-blue-600" />
            Filters
          </CardTitle>
          <CardDescription className="text-gray-600">
            Search and filter your leads to find exactly what you're looking for.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Status</label>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="NEW">🆕 New</option>
                <option value="CONTACTED">📞 Contacted</option>
                <option value="INTERESTED">⭐ Interested</option>
                <option value="CONVERTED">✅ Converted</option>
                <option value="LOST">❌ Lost</option>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Source</label>
              <Input
                placeholder="Filter by source..."
                value={sourceFilter}
                onChange={(e) => setSourceFilter(e.target.value)}
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Leads Table */}
      <Card className="shadow-sm border-gray-200">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">
                Leads ({filteredLeads.length})
              </CardTitle>
              <CardDescription className="text-gray-600">
                Manage your leads and track their progress through the sales pipeline.
              </CardDescription>
            </div>
            {filteredLeads.length > 0 && (
              <div className="text-sm text-gray-500">
                {filteredLeads.length} of {leads.length} leads
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600 font-medium">Loading leads...</p>
            </div>
          ) : filteredLeads.length === 0 ? (
            <div className="text-center py-16">
              <div className="mx-auto h-16 w-16 text-gray-400 mb-4">
                <Users className="h-16 w-16" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter || sourceFilter
                  ? "Try adjusting your search or filter criteria."
                  : "Get started by adding your first lead."
                }
              </p>
              {!searchTerm && !statusFilter && !sourceFilter && (
                <Button
                  onClick={() => setIsCreateDialogOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Lead
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">Name</TableHead>
                  <TableHead className="min-w-[120px]">Location</TableHead>
                  <TableHead className="min-w-[120px]">Phone</TableHead>
                  <TableHead className="min-w-[200px]">Email</TableHead>
                  <TableHead className="min-w-[150px]">Website</TableHead>
                  <TableHead className="min-w-[200px]">Notes</TableHead>
                  <TableHead className="min-w-[100px]">Source</TableHead>
                  <TableHead className="min-w-[100px]">Status</TableHead>
                  <TableHead className="text-right min-w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLeads.map((lead) => (
                  <TableRow key={lead.id}>
                    <TableCell className="font-medium">
                      <div className="space-y-1">
                        <div>{lead.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {lead._count.leadNotes || 0} notes • {lead._count.followUps} follow-ups
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {lead.location ? (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{lead.location}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {lead.phone ? (
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{lead.phone}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {lead.email ? (
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{lead.email}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {lead.website ? (
                        <div className="flex items-center gap-1">
                          <Globe className="h-3 w-3 text-muted-foreground" />
                          <a
                            href={lead.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:underline"
                          >
                            {lead.website.replace(/^https?:\/\//, '')}
                          </a>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {lead.notes ? (
                        <div className="max-w-xs">
                          <p className="text-sm text-gray-600 truncate" title={lead.notes}>
                            {lead.notes}
                          </p>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {lead.source ? (
                        <span className="text-sm">{lead.source}</span>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={`${statusColors[lead.status]} border font-medium`}>
                        <span className="mr-1">{statusIcons[lead.status]}</span>
                        {lead.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-1 justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(lead)}
                          className="h-8 w-8 p-0 border-gray-300 hover:border-blue-500 hover:text-blue-600"
                          title="Edit lead"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(lead.id)}
                          className="h-8 w-8 p-0 border-gray-300 hover:border-red-500 hover:text-red-600"
                          title="Delete lead"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog
        open={isCreateDialogOpen || isEditDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreateDialogOpen(false)
            setIsEditDialogOpen(false)
            setEditingLead(null)
            form.reset()
          }
        }}
      >
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-xl font-semibold">
              {editingLead ? 'Edit Lead' : 'Add New Lead'}
            </DialogTitle>
            <p className="text-sm text-muted-foreground">
              {editingLead ? 'Update the lead information below.' : 'Fill in the details to create a new lead.'}
            </p>
          </DialogHeader>

          <Form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Basic Information Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900 border-b pb-2">Basic Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField>
                  <FormLabel htmlFor="name" className="text-sm font-medium text-gray-700">
                    Name <span className="text-red-500">*</span>
                  </FormLabel>
                  <Input
                    id="name"
                    placeholder="Enter lead name"
                    className="mt-1"
                    {...form.register('name')}
                  />
                  {form.formState.errors.name && (
                    <FormMessage>{form.formState.errors.name.message}</FormMessage>
                  )}
                </FormField>

                <FormField>
                  <FormLabel htmlFor="location" className="text-sm font-medium text-gray-700">
                    Location
                  </FormLabel>
                  <Input
                    id="location"
                    placeholder="City, State, Country"
                    className="mt-1"
                    {...form.register('location')}
                  />
                </FormField>
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900 border-b pb-2">Contact Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField>
                  <FormLabel htmlFor="phone" className="text-sm font-medium text-gray-700">
                    Phone
                  </FormLabel>
                  <Input
                    id="phone"
                    placeholder="+****************"
                    className="mt-1"
                    {...form.register('phone')}
                  />
                </FormField>

                <FormField>
                  <FormLabel htmlFor="email" className="text-sm font-medium text-gray-700">
                    Email
                  </FormLabel>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="mt-1"
                    {...form.register('email')}
                  />
                  {form.formState.errors.email && (
                    <FormMessage>{form.formState.errors.email.message}</FormMessage>
                  )}
                </FormField>
              </div>

              <FormField>
                <FormLabel htmlFor="website" className="text-sm font-medium text-gray-700">
                  Website
                </FormLabel>
                <Input
                  id="website"
                  type="url"
                  placeholder="https://example.com"
                  className="mt-1"
                  {...form.register('website')}
                />
                {form.formState.errors.website && (
                  <FormMessage>{form.formState.errors.website.message}</FormMessage>
                )}
              </FormField>
            </div>

            {/* Lead Details Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900 border-b pb-2">Lead Details</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField>
                  <FormLabel htmlFor="source" className="text-sm font-medium text-gray-700">
                    Source
                  </FormLabel>
                  <Input
                    id="source"
                    placeholder="Website, Referral, Cold Call, etc."
                    className="mt-1"
                    {...form.register('source')}
                  />
                </FormField>

                <FormField>
                  <FormLabel htmlFor="status" className="text-sm font-medium text-gray-700">
                    Status
                  </FormLabel>
                  <Select {...form.register('status')} className="mt-1">
                    <option value="NEW">🆕 New</option>
                    <option value="CONTACTED">📞 Contacted</option>
                    <option value="INTERESTED">⭐ Interested</option>
                    <option value="CONVERTED">✅ Converted</option>
                    <option value="LOST">❌ Lost</option>
                  </Select>
                </FormField>
              </div>

              <FormField>
                <FormLabel htmlFor="notes" className="text-sm font-medium text-gray-700">
                  Notes
                </FormLabel>
                <Textarea
                  id="notes"
                  rows={4}
                  placeholder="Add any additional notes about this lead..."
                  className="mt-1 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  {...form.register('notes')}
                />
              </FormField>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-6 border-t">
              <Button type="submit" className="flex-1 bg-blue-600 hover:bg-blue-700">
                {editingLead ? '💾 Update Lead' : '✨ Create Lead'}
              </Button>
              <Button
                type="button"
                variant="outline"
                className="px-6"
                onClick={() => {
                  setIsCreateDialogOpen(false)
                  setIsEditDialogOpen(false)
                  setEditingLead(null)
                  form.reset()
                }}
              >
                Cancel
              </Button>
            </div>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
