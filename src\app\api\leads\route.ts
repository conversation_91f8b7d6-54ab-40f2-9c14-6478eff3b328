import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getUserFromRequest } from '@/lib/auth'
import { leadSchema } from '@/lib/validations'

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const source = searchParams.get('source')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      userId: user.userId,
    }

    if (status) {
      where.status = status
    }

    if (source) {
      where.source = {
        contains: source,
        mode: 'insensitive',
      }
    }

    // Get leads with pagination
    const [leads, total] = await Promise.all([
      prisma.lead.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
        include: {
          leadNotes: {
            orderBy: { createdAt: 'desc' },
            take: 3, // Include latest 3 notes
          },
          followUps: {
            where: { completed: false },
            orderBy: { dueDate: 'asc' },
            take: 1, // Include next upcoming follow-up
          },
          _count: {
            select: {
              leadNotes: true,
              followUps: true,
            },
          },
        },
      }),
      prisma.lead.count({ where }),
    ])

    return NextResponse.json({
      leads,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.log(error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = leadSchema.parse(body)

    // Check if lead with same email already exists for this user
    const existingLead = await prisma.lead.findFirst({
      where: {
        userId: user.userId,
        email: validatedData.email,
      },
    })

    if (existingLead) {
      return NextResponse.json(
        { error: 'Lead with this email already exists' },
        { status: 400 }
      )
    }

    const lead = await prisma.lead.create({
      data: {
        ...validatedData,
        userId: user.userId,
      },
      include: {
        leadNotes: true,
        followUps: true,
        _count: {
          select: {
            leadNotes: true,
            followUps: true,
          },
        },
      },
    })

    return NextResponse.json(lead, { status: 201 })
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid input data', details: error },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
